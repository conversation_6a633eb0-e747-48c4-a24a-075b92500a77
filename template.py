from dataclasses import dataclass, field
from typing import TypedDict, NotRequired, Literal, Required


class RelativePosition(TypedDict):
    """TypedDict for relative positioning in keyframes and ranges"""
    type: Literal['pre', 'middle', 'tail']
    position: Literal['start', 'end']
    offset_frame: NotRequired[int]  # Optional frame offset
    index: NotRequired[int]  # Optional index for middle segments


class TrackRange(TypedDict):
    """TypedDict for track range definition"""
    start: RelativePosition
    end: RelativePosition


class Vector2D(TypedDict):
    """TypedDict for 2D vector (x, y coordinates)"""
    x: Required[float]
    y: Required[float]


class EffectParams(TypedDict, total=False):
    """TypedDict for effect parameters - flexible structure"""
    intensity: float
    duration: float
    color: str
    blend_mode: str
    opacity: float
    scale: float
    value: float  # Single value parameter
    # Effect parameters vary by effect type


class MaskConfig(TypedDict, total=False):
    """TypedDict for mask configuration"""
    type: str  # 'rectangle', 'circle', 'polygon', etc.
    points: NotRequired[list[Vector2D]]
    radius: NotRequired[float]
    width: NotRequired[float]
    height: NotRequired[float]
    round_corner: NotRequired[float]
    position: NotRequired[Vector2D]
    feather: NotRequired[float]
    center: NotRequired[Vector2D]


class KeyframeDict(TypedDict):
    """TypedDict for keyframe input data"""
    relative: RelativePosition  # Required field
    scale: NotRequired[Vector2D]
    position: NotRequired[Vector2D]
    rotation: NotRequired[float]
    alpha: NotRequired[float]


class TrackDict(TypedDict):
    """TypedDict for track input data"""
    type: Literal['video', 'audio', 'effect', 'image', 'text']  # Required
    timing: Literal['relative', 'absolute']  # Required
    keyframe: NotRequired[list[KeyframeDict]]
    index: NotRequired[int]
    name: NotRequired[str]
    params: NotRequired[EffectParams]
    mask: NotRequired[MaskConfig]
    round_corner: NotRequired[int]
    position: NotRequired[Vector2D]
    feather: NotRequired[int]
    center: NotRequired[Vector2D]
    image: NotRequired[str]
    alpha: NotRequired[float]
    effect: NotRequired[str]
    range: NotRequired[TrackRange]


class ParagraphDict(TypedDict, total=False):
    """TypedDict for paragraph input data"""
    track: NotRequired[list[TrackDict]]


class MetaDict(TypedDict, total=False):
    """TypedDict for meta information"""
    title: NotRequired[str]
    description: NotRequired[str]
    version: NotRequired[str]
    name: NotRequired[str]
    videos: NotRequired[int]  # Number of videos required
    images: NotRequired[int]  # Number of images required
    canvas_width: NotRequired[int]  # Canvas width in pixels
    canvas_height: NotRequired[int]  # Canvas height in pixels


class VideoTemplateDict(TypedDict, total=False):
    """TypedDict for video template input data"""
    meta: NotRequired[MetaDict]
    paragraph: NotRequired[list[ParagraphDict]]


@dataclass
class KeyframeConfig:
    """Represents a single keyframe configuration from TOML"""
    relative: RelativePosition  # Structured type for relative positioning
    scale: Vector2D | None = None  # x, y
    position: Vector2D | None = None  # x, y
    rotation: float | None = None
    alpha: float | None = None
    
    @classmethod
    def from_dict(cls, data: KeyframeDict) -> 'KeyframeConfig':
        """Create KeyframeConfig from dictionary"""
        return cls(
            relative=data['relative'],
            scale=data.get('scale'),
            position=data.get('position'),
            rotation=data.get('rotation'),
            alpha=data.get('alpha')
        )


@dataclass
class TrackTemplate:
    """Represents a track with its effects and keyframes"""
    type: str  # video/audio/effect/image/text
    timing: str  # relative/absolute
    keyframes: list[KeyframeConfig] = field(default_factory=list)
    index: int | None = None  # for video tracks - which video material to use
    name: str | None = None  # for effects
    params: EffectParams | None = None  # effect parameters
    mask: MaskConfig | None = None  # mask configuration
    round_corner: int | None = None
    position: Vector2D | None = None
    feather: int | None = None
    center: Vector2D | None = None
    image: str | None = None  # for image tracks
    alpha: float | None = None  # for image tracks
    
    # Attached effect fields (for video segments)
    effect: str | None = None  # effect name to attach to video segment
    
    # Track lifecycle range (optional) - defines when track is active
    range: TrackRange | None = None  # start/end with type, position
    
    @classmethod
    def from_dict(cls, data: TrackDict) -> 'TrackTemplate':
        """Create TrackTemplate from dictionary"""
        # Extract keyframes if present
        keyframes = []
        if 'keyframe' in data:
            keyframes = [KeyframeConfig.from_dict(kf) for kf in data['keyframe']]
        
        return cls(
            type=data['type'],
            timing=data['timing'],
            keyframes=keyframes,
            index=data.get('index'),
            name=data.get('name'),
            params=data.get('params'),
            mask=data.get('mask'),
            round_corner=data.get('round_corner'),
            position=data.get('position'),
            feather=data.get('feather'),
            center=data.get('center'),
            image=data.get('image'),
            alpha=data.get('alpha'),
            effect=data.get('effect'),
            range=data.get('range')
        )


@dataclass
class ParagraphTemplate:
    """Template for a complete paragraph section"""
    tracks: list[TrackTemplate] = field(default_factory=list)
    
    @classmethod
    def from_dict(cls, data: ParagraphDict) -> 'ParagraphTemplate':
        """Create ParagraphTemplate from dictionary"""
        tracks = []
        if 'track' in data:
            tracks = [TrackTemplate.from_dict(track) for track in data['track']]
        return cls(tracks=tracks)


@dataclass
class VideoTemplate:
    """Main template container"""
    meta: MetaDict
    paragraphs: list[ParagraphTemplate] = field(default_factory=list)
    
    @classmethod
    def from_dict(cls, data: VideoTemplateDict) -> 'VideoTemplate':
        """Create VideoTemplate from dictionary"""
        meta = data.get('meta', {})
        paragraphs = []
        if 'paragraph' in data:
            paragraphs = [ParagraphTemplate.from_dict(p) for p in data['paragraph']]
        return cls(meta=meta, paragraphs=paragraphs)