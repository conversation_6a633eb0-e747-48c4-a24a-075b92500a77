Use `basedpyright` to check the file user specified.  
file_path: $ARGUMENTS  

Additional static-type constraints (ultrathink):

1. Run in `--type-checking-mode recommanded` and treat **any diagnostic ≥ warning as failure**.  
2. Forbid the following symbols anywhere in the AST  
   • `Any` • `Union` • `Dict` • `List` • `Optional`  
   –  use concrete built-ins (`list[T]`, `dict[K, V]`) only when shape is simple.  
3. If a mapping’s structure is non-trivial  
   –  define a `TypedDict` (or `@dataclass`, `NamedTuple`) instead of an untyped `dict`.  
4. All public functions and methods must have  
   –  explicit parameter and return annotations (no implicit `Any`)  
   –  no bare *untyped* `*args` / `**kwargs`.  
5. No implicit `None`-returning functions; spell the return type as `None`.  
6. Prefer `Literal`, `Enum`, `Protocol`, `@overload` or separate functions to express variation instead of `Union` / `Optional`.  
7. Disallow `type: ignore` unless justified by an inline comment that matches   `# type: ignore\[CODE\]  – reason…`.  
8. Reject unused imports, unreachable code and any diagnostic listed in the **“all”** preset of basedpyright.

Pass the check only when **basedpyright exits with code 0**.
